<?php

namespace App\Filament\Pos\Resources;

use App\Filament\Pos\Resources\PriceListResource\Pages;
use App\Filament\Pos\Resources\PriceListResource\RelationManagers;
use App\Models\PriceList;
use App\Models\Product;
use App\Models\Outlet;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;

class PriceListResource extends Resource
{
    protected static ?string $model = PriceList::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationLabel = 'Price Lists';

    protected static ?string $modelLabel = 'Price List';

    protected static ?string $pluralModelLabel = 'Price Lists';

    protected static ?int $navigationSort = 3;

    protected static ?string $navigationGroup = 'Product Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Basic Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Name')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('e.g., VIP Customer Prices'),

                                TextInput::make('code')
                                    ->label('Code')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->maxLength(50)
                                    ->placeholder('e.g., VIP_001')
                                    ->alphaDash(),
                            ]),

                        Textarea::make('description')
                            ->label('Description')
                            ->rows(3)
                            ->placeholder('Describe this price list...'),
                    ]),

                Section::make('Settings')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                Toggle::make('is_global')
                                    ->label('Global Price List')
                                    ->helperText('Global price lists are used as fallback when outlet-specific prices are not available')
                                    ->disabled(fn ($record) => $record && $record->is_global), // Prevent disabling existing global

                                Toggle::make('is_active')
                                    ->label('Active')
                                    ->default(true),

                                Select::make('created_by')
                                    ->label('Created By')
                                    ->relationship('creator', 'name')
                                    ->default(auth()->id())
                                    ->disabled()
                                    ->dehydrated(),
                            ]),

                        Grid::make(2)
                            ->schema([
                                DatePicker::make('effective_from')
                                    ->label('Effective From')
                                    ->helperText('Leave empty for immediate effect'),

                                DatePicker::make('effective_until')
                                    ->label('Effective Until')
                                    ->helperText('Leave empty for no expiration'),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('code')
                    ->label('Code')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                BadgeColumn::make('is_global')
                    ->label('Type')
                    ->getStateUsing(fn ($record) => $record->is_global ? 'Global' : 'Outlet-Specific')
                    ->colors([
                        'primary' => 'Global',
                        'secondary' => 'Outlet-Specific',
                    ]),

                TextColumn::make('products_count')
                    ->label('Products')
                    ->getStateUsing(fn ($record) => $record->activeItems()->count())
                    ->sortable(),

                TextColumn::make('outlets_count')
                    ->label('Outlets')
                    ->getStateUsing(fn ($record) => $record->activeOutlets()->count())
                    ->sortable(),

                BooleanColumn::make('is_active')
                    ->label('Active')
                    ->sortable(),

                TextColumn::make('effective_from')
                    ->label('Effective From')
                    ->date()
                    ->sortable()
                    ->placeholder('Immediate'),

                TextColumn::make('effective_until')
                    ->label('Effective Until')
                    ->date()
                    ->sortable()
                    ->placeholder('No expiration'),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TernaryFilter::make('is_global')
                    ->label('Type')
                    ->placeholder('All price lists')
                    ->trueLabel('Global only')
                    ->falseLabel('Outlet-specific only'),

                TernaryFilter::make('is_active')
                    ->label('Status')
                    ->placeholder('All statuses')
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only'),

                Filter::make('effective')
                    ->label('Currently Effective')
                    ->query(fn (Builder $query): Builder => 
                        $query->where('is_active', true)
                            ->where(function ($q) {
                                $q->whereNull('effective_from')
                                  ->orWhere('effective_from', '<=', now()->toDateString());
                            })
                            ->where(function ($q) {
                                $q->whereNull('effective_until')
                                  ->orWhere('effective_until', '>=', now()->toDateString());
                            })
                    ),
            ])
            ->actions([
                Action::make('manage_products')
                    ->label('Manage Products')
                    ->icon('heroicon-o-cube')
                    ->url(fn (PriceList $record): string => route('filament.pos.resources.price-lists.manage-products', $record)),

                Action::make('assign_outlets')
                    ->label('Assign Outlets')
                    ->icon('heroicon-o-building-storefront')
                    ->url(fn (PriceList $record): string => route('filament.pos.resources.price-lists.assign-outlets', $record))
                    ->visible(fn (PriceList $record): bool => !$record->is_global),

                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->before(function (PriceList $record) {
                        // Prevent deletion of global price list if it's the only one
                        if ($record->is_global && PriceList::where('is_global', true)->count() === 1) {
                            throw new \Exception('Cannot delete the only global price list.');
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ItemsRelationManager::class,
            RelationManagers\OutletsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPriceLists::route('/'),
            'create' => Pages\CreatePriceList::route('/create'),
            'edit' => Pages\EditPriceList::route('/{record}/edit'),
            'manage-products' => Pages\ManageProducts::route('/{record}/products'),
            'assign-outlets' => Pages\AssignOutlets::route('/{record}/outlets'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
