<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Outlet;
use App\Models\PriceList;
use App\Models\PriceListItem;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class PriceResolutionService
{
    /**
     * Cache duration for price resolution (in minutes)
     */
    protected int $cacheDuration = 60;

    /**
     * Get the price for a product at a specific outlet
     * 
     * @param int $productId
     * @param int $outletId
     * @return array
     */
    public function getProductPrice(int $productId, int $outletId): array
    {
        $cacheKey = "price_resolution_{$productId}_{$outletId}";
        
        return Cache::remember($cacheKey, $this->cacheDuration, function () use ($productId, $outletId) {
            return $this->resolveProductPrice($productId, $outletId);
        });
    }

    /**
     * Get prices for multiple products at a specific outlet
     * 
     * @param array $productIds
     * @param int $outletId
     * @return array
     */
    public function getProductPrices(array $productIds, int $outletId): array
    {
        $prices = [];
        
        foreach ($productIds as $productId) {
            $prices[$productId] = $this->getProductPrice($productId, $outletId);
        }
        
        return $prices;
    }

    /**
     * Resolve the price for a product at an outlet
     * 
     * @param int $productId
     * @param int $outletId
     * @return array
     */
    protected function resolveProductPrice(int $productId, int $outletId): array
    {
        $product = Product::find($productId);
        $outlet = Outlet::find($outletId);

        if (!$product || !$outlet) {
            return $this->getDefaultPriceInfo($product);
        }

        // Step 1: Check outlet-specific price lists (ordered by priority)
        $outletPriceLists = $outlet->activePriceLists()
            ->where('price_lists.is_active', true)
            ->effective()
            ->orderBy('outlet_price_lists.priority')
            ->get();

        foreach ($outletPriceLists as $priceList) {
            $priceListItem = $this->findProductInPriceList($productId, $priceList->id);
            
            if ($priceListItem) {
                return [
                    'price' => $priceListItem->price,
                    'cost_price' => $priceListItem->cost_price ?: $product->cost_price,
                    'source' => 'outlet_price_list',
                    'price_list_id' => $priceList->id,
                    'price_list_name' => $priceList->name,
                    'priority' => $priceList->pivot->priority,
                ];
            }
        }

        // Step 2: Check global price list
        $globalPriceList = PriceList::getGlobal();
        if ($globalPriceList && $globalPriceList->isEffective()) {
            $globalPriceListItem = $this->findProductInPriceList($productId, $globalPriceList->id);
            
            if ($globalPriceListItem) {
                return [
                    'price' => $globalPriceListItem->price,
                    'cost_price' => $globalPriceListItem->cost_price ?: $product->cost_price,
                    'source' => 'global_price_list',
                    'price_list_id' => $globalPriceList->id,
                    'price_list_name' => $globalPriceList->name,
                    'priority' => null,
                ];
            }
        }

        // Step 3: Fallback to product's default price
        return $this->getDefaultPriceInfo($product);
    }

    /**
     * Find a product in a specific price list
     * 
     * @param int $productId
     * @param int $priceListId
     * @return PriceListItem|null
     */
    protected function findProductInPriceList(int $productId, int $priceListId): ?PriceListItem
    {
        return PriceListItem::where('product_id', $productId)
            ->where('price_list_id', $priceListId)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Get default price information for a product
     * 
     * @param Product|null $product
     * @return array
     */
    protected function getDefaultPriceInfo(?Product $product): array
    {
        if (!$product) {
            return [
                'price' => 0,
                'cost_price' => 0,
                'source' => 'not_found',
                'price_list_id' => null,
                'price_list_name' => null,
                'priority' => null,
            ];
        }

        return [
            'price' => $product->price,
            'cost_price' => $product->cost_price,
            'source' => 'product_default',
            'price_list_id' => null,
            'price_list_name' => 'Default Product Price',
            'priority' => null,
        ];
    }

    /**
     * Clear price cache for a specific product and outlet
     * 
     * @param int $productId
     * @param int $outletId
     * @return void
     */
    public function clearPriceCache(int $productId, int $outletId): void
    {
        $cacheKey = "price_resolution_{$productId}_{$outletId}";
        Cache::forget($cacheKey);
    }

    /**
     * Clear price cache for a product across all outlets
     * 
     * @param int $productId
     * @return void
     */
    public function clearProductPriceCache(int $productId): void
    {
        $outlets = Outlet::where('is_active', true)->pluck('id');
        
        foreach ($outlets as $outletId) {
            $this->clearPriceCache($productId, $outletId);
        }
    }

    /**
     * Clear price cache for all products at a specific outlet
     * 
     * @param int $outletId
     * @return void
     */
    public function clearOutletPriceCache(int $outletId): void
    {
        $products = Product::where('is_active', true)->pluck('id');
        
        foreach ($products as $productId) {
            $this->clearPriceCache($productId, $outletId);
        }
    }

    /**
     * Clear all price caches
     * 
     * @return void
     */
    public function clearAllPriceCaches(): void
    {
        $outlets = Outlet::where('is_active', true)->pluck('id');
        $products = Product::where('is_active', true)->pluck('id');
        
        foreach ($outlets as $outletId) {
            foreach ($products as $productId) {
                $this->clearPriceCache($productId, $outletId);
            }
        }
    }

    /**
     * Get price resolution summary for debugging
     * 
     * @param int $productId
     * @param int $outletId
     * @return array
     */
    public function getPriceResolutionSummary(int $productId, int $outletId): array
    {
        $product = Product::find($productId);
        $outlet = Outlet::find($outletId);

        if (!$product || !$outlet) {
            return [
                'error' => 'Product or outlet not found',
                'product_exists' => (bool) $product,
                'outlet_exists' => (bool) $outlet,
            ];
        }

        $summary = [
            'product' => [
                'id' => $product->id,
                'name' => $product->name,
                'default_price' => $product->price,
                'default_cost_price' => $product->cost_price,
            ],
            'outlet' => [
                'id' => $outlet->id,
                'name' => $outlet->name,
                'code' => $outlet->code,
            ],
            'resolution_steps' => [],
        ];

        // Check outlet-specific price lists
        $outletPriceLists = $outlet->activePriceLists()
            ->where('price_lists.is_active', true)
            ->effective()
            ->orderBy('outlet_price_lists.priority')
            ->get();

        foreach ($outletPriceLists as $priceList) {
            $priceListItem = $this->findProductInPriceList($productId, $priceList->id);
            
            $summary['resolution_steps'][] = [
                'step' => 'outlet_price_list',
                'price_list_id' => $priceList->id,
                'price_list_name' => $priceList->name,
                'priority' => $priceList->pivot->priority,
                'product_found' => (bool) $priceListItem,
                'price' => $priceListItem ? $priceListItem->price : null,
                'cost_price' => $priceListItem ? $priceListItem->cost_price : null,
            ];

            if ($priceListItem) {
                $summary['final_resolution'] = [
                    'source' => 'outlet_price_list',
                    'price_list_id' => $priceList->id,
                    'price_list_name' => $priceList->name,
                    'price' => $priceListItem->price,
                    'cost_price' => $priceListItem->cost_price ?: $product->cost_price,
                ];
                return $summary;
            }
        }

        // Check global price list
        $globalPriceList = PriceList::getGlobal();
        if ($globalPriceList && $globalPriceList->isEffective()) {
            $globalPriceListItem = $this->findProductInPriceList($productId, $globalPriceList->id);
            
            $summary['resolution_steps'][] = [
                'step' => 'global_price_list',
                'price_list_id' => $globalPriceList->id,
                'price_list_name' => $globalPriceList->name,
                'product_found' => (bool) $globalPriceListItem,
                'price' => $globalPriceListItem ? $globalPriceListItem->price : null,
                'cost_price' => $globalPriceListItem ? $globalPriceListItem->cost_price : null,
            ];

            if ($globalPriceListItem) {
                $summary['final_resolution'] = [
                    'source' => 'global_price_list',
                    'price_list_id' => $globalPriceList->id,
                    'price_list_name' => $globalPriceList->name,
                    'price' => $globalPriceListItem->price,
                    'cost_price' => $globalPriceListItem->cost_price ?: $product->cost_price,
                ];
                return $summary;
            }
        }

        // Fallback to default
        $summary['resolution_steps'][] = [
            'step' => 'product_default',
            'price' => $product->price,
            'cost_price' => $product->cost_price,
        ];

        $summary['final_resolution'] = [
            'source' => 'product_default',
            'price_list_id' => null,
            'price_list_name' => 'Default Product Price',
            'price' => $product->price,
            'cost_price' => $product->cost_price,
        ];

        return $summary;
    }

    /**
     * Validate price list configuration for an outlet
     * 
     * @param int $outletId
     * @return array
     */
    public function validateOutletPriceConfiguration(int $outletId): array
    {
        $outlet = Outlet::find($outletId);
        
        if (!$outlet) {
            return [
                'valid' => false,
                'errors' => ['Outlet not found'],
            ];
        }

        $errors = [];
        $warnings = [];

        // Check if outlet has any price lists assigned
        $assignedPriceLists = $outlet->activePriceLists()->count();
        
        if ($assignedPriceLists === 0) {
            $warnings[] = 'No price lists assigned to this outlet. Will use global price list or product defaults.';
        }

        // Check for duplicate priorities
        $priorities = $outlet->activePriceLists()
            ->pluck('outlet_price_lists.priority')
            ->toArray();
        
        if (count($priorities) !== count(array_unique($priorities))) {
            $errors[] = 'Duplicate priorities found in assigned price lists.';
        }

        // Check if global price list exists
        $globalPriceList = PriceList::getGlobal();
        if (!$globalPriceList) {
            $warnings[] = 'No global price list found. Products not in outlet-specific price lists will use default product prices.';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
            'assigned_price_lists_count' => $assignedPriceLists,
            'has_global_price_list' => (bool) $globalPriceList,
        ];
    }
}
