1753929841a:3:{s:8:"products";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:9:{i:0;O:18:"App\Models\Product":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1;s:4:"name";s:3:"Kue";s:3:"sku";s:7:"KUE4198";s:5:"price";s:8:"10000.00";s:10:"cost_price";s:7:"8000.00";s:11:"category_id";i:1;s:14:"stock_quantity";i:10;s:12:"is_food_item";i:0;}s:11:" * original";a:8:{s:2:"id";i:1;s:4:"name";s:3:"Kue";s:3:"sku";s:7:"KUE4198";s:5:"price";s:8:"10000.00";s:10:"cost_price";s:7:"8000.00";s:11:"category_id";i:1;s:14:"stock_quantity";i:10;s:12:"is_food_item";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:5:"price";s:9:"decimal:2";s:10:"cost_price";s:9:"decimal:2";s:14:"stock_quantity";s:7:"integer";s:9:"is_active";s:7:"boolean";s:12:"is_food_item";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";O:19:"App\Models\Category":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:1;s:4:"name";s:7:"Makanan";s:11:"description";N;s:10:"created_at";s:19:"2025-07-24 11:13:53";s:10:"updated_at";s:19:"2025-07-24 11:13:53";s:10:"deleted_at";N;}s:11:" * original";a:6:{s:2:"id";i:1;s:4:"name";s:7:"Makanan";s:11:"description";N;s:10:"created_at";s:19:"2025-07-24 11:13:53";s:10:"updated_at";s:19:"2025-07-24 11:13:53";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:4:"name";i:1;s:11:"description";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:8:" * dates";a:1:{i:0;s:10:"deleted_at";}s:16:" * forceDeleting";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:10:{i:0;s:4:"name";i:1;s:3:"sku";i:2;s:7:"barcode";i:3;s:11:"description";i:4;s:5:"price";i:5;s:10:"cost_price";i:6;s:11:"category_id";i:7;s:14:"stock_quantity";i:8;s:9:"is_active";i:9;s:12:"is_food_item";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:8:" * dates";a:1:{i:0;s:10:"deleted_at";}s:16:" * forceDeleting";b:0;}i:1;O:18:"App\Models\Product":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:2;s:4:"name";s:4:"Roti";s:3:"sku";s:8:"ROTI4831";s:5:"price";s:7:"5000.00";s:10:"cost_price";s:7:"3000.00";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:12:"is_food_item";i:1;}s:11:" * original";a:8:{s:2:"id";i:2;s:4:"name";s:4:"Roti";s:3:"sku";s:8:"ROTI4831";s:5:"price";s:7:"5000.00";s:10:"cost_price";s:7:"3000.00";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:12:"is_food_item";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:5:"price";s:9:"decimal:2";s:10:"cost_price";s:9:"decimal:2";s:14:"stock_quantity";s:7:"integer";s:9:"is_active";s:7:"boolean";s:12:"is_food_item";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:50;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:10:{i:0;s:4:"name";i:1;s:3:"sku";i:2;s:7:"barcode";i:3;s:11:"description";i:4;s:5:"price";i:5;s:10:"cost_price";i:6;s:11:"category_id";i:7;s:14:"stock_quantity";i:8;s:9:"is_active";i:9;s:12:"is_food_item";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:8:" * dates";a:1:{i:0;s:10:"deleted_at";}s:16:" * forceDeleting";b:0;}i:2;O:18:"App\Models\Product":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:3;s:4:"name";s:4:"Kopi";s:3:"sku";s:8:"KOPI6234";s:5:"price";s:7:"8000.00";s:10:"cost_price";s:7:"6500.00";s:11:"category_id";i:2;s:14:"stock_quantity";i:5;s:12:"is_food_item";i:0;}s:11:" * original";a:8:{s:2:"id";i:3;s:4:"name";s:4:"Kopi";s:3:"sku";s:8:"KOPI6234";s:5:"price";s:7:"8000.00";s:10:"cost_price";s:7:"6500.00";s:11:"category_id";i:2;s:14:"stock_quantity";i:5;s:12:"is_food_item";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:5:"price";s:9:"decimal:2";s:10:"cost_price";s:9:"decimal:2";s:14:"stock_quantity";s:7:"integer";s:9:"is_active";s:7:"boolean";s:12:"is_food_item";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";O:19:"App\Models\Category":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:2;s:4:"name";s:7:"Minuman";s:11:"description";N;s:10:"created_at";s:19:"2025-07-27 13:52:31";s:10:"updated_at";s:19:"2025-07-27 13:52:31";s:10:"deleted_at";N;}s:11:" * original";a:6:{s:2:"id";i:2;s:4:"name";s:7:"Minuman";s:11:"description";N;s:10:"created_at";s:19:"2025-07-27 13:52:31";s:10:"updated_at";s:19:"2025-07-27 13:52:31";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:4:"name";i:1;s:11:"description";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:8:" * dates";a:1:{i:0;s:10:"deleted_at";}s:16:" * forceDeleting";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:10:{i:0;s:4:"name";i:1;s:3:"sku";i:2;s:7:"barcode";i:3;s:11:"description";i:4;s:5:"price";i:5;s:10:"cost_price";i:6;s:11:"category_id";i:7;s:14:"stock_quantity";i:8;s:9:"is_active";i:9;s:12:"is_food_item";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:8:" * dates";a:1:{i:0;s:10:"deleted_at";}s:16:" * forceDeleting";b:0;}i:3;O:18:"App\Models\Product":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:4;s:4:"name";s:4:"Kopi";s:3:"sku";s:8:"KOPI1289";s:5:"price";s:7:"8000.00";s:10:"cost_price";s:7:"6500.00";s:11:"category_id";i:2;s:14:"stock_quantity";i:5;s:12:"is_food_item";i:0;}s:11:" * original";a:8:{s:2:"id";i:4;s:4:"name";s:4:"Kopi";s:3:"sku";s:8:"KOPI1289";s:5:"price";s:7:"8000.00";s:10:"cost_price";s:7:"6500.00";s:11:"category_id";i:2;s:14:"stock_quantity";i:5;s:12:"is_food_item";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:5:"price";s:9:"decimal:2";s:10:"cost_price";s:9:"decimal:2";s:14:"stock_quantity";s:7:"integer";s:9:"is_active";s:7:"boolean";s:12:"is_food_item";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:235;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:10:{i:0;s:4:"name";i:1;s:3:"sku";i:2;s:7:"barcode";i:3;s:11:"description";i:4;s:5:"price";i:5;s:10:"cost_price";i:6;s:11:"category_id";i:7;s:14:"stock_quantity";i:8;s:9:"is_active";i:9;s:12:"is_food_item";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:8:" * dates";a:1:{i:0;s:10:"deleted_at";}s:16:" * forceDeleting";b:0;}i:4;O:18:"App\Models\Product":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:5;s:4:"name";s:6:"Nugget";s:3:"sku";s:10:"NUGGET8709";s:5:"price";s:7:"8000.00";s:10:"cost_price";s:7:"5000.00";s:11:"category_id";i:1;s:14:"stock_quantity";i:8;s:12:"is_food_item";i:1;}s:11:" * original";a:8:{s:2:"id";i:5;s:4:"name";s:6:"Nugget";s:3:"sku";s:10:"NUGGET8709";s:5:"price";s:7:"8000.00";s:10:"cost_price";s:7:"5000.00";s:11:"category_id";i:1;s:14:"stock_quantity";i:8;s:12:"is_food_item";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:5:"price";s:9:"decimal:2";s:10:"cost_price";s:9:"decimal:2";s:14:"stock_quantity";s:7:"integer";s:9:"is_active";s:7:"boolean";s:12:"is_food_item";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:50;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:10:{i:0;s:4:"name";i:1;s:3:"sku";i:2;s:7:"barcode";i:3;s:11:"description";i:4;s:5:"price";i:5;s:10:"cost_price";i:6;s:11:"category_id";i:7;s:14:"stock_quantity";i:8;s:9:"is_active";i:9;s:12:"is_food_item";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:8:" * dates";a:1:{i:0;s:10:"deleted_at";}s:16:" * forceDeleting";b:0;}i:5;O:18:"App\Models\Product":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:6;s:4:"name";s:5:"Sosis";s:3:"sku";s:9:"SOSIS8846";s:5:"price";s:7:"6000.00";s:10:"cost_price";s:7:"5000.00";s:11:"category_id";i:1;s:14:"stock_quantity";i:20;s:12:"is_food_item";i:1;}s:11:" * original";a:8:{s:2:"id";i:6;s:4:"name";s:5:"Sosis";s:3:"sku";s:9:"SOSIS8846";s:5:"price";s:7:"6000.00";s:10:"cost_price";s:7:"5000.00";s:11:"category_id";i:1;s:14:"stock_quantity";i:20;s:12:"is_food_item";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:5:"price";s:9:"decimal:2";s:10:"cost_price";s:9:"decimal:2";s:14:"stock_quantity";s:7:"integer";s:9:"is_active";s:7:"boolean";s:12:"is_food_item";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:50;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:10:{i:0;s:4:"name";i:1;s:3:"sku";i:2;s:7:"barcode";i:3;s:11:"description";i:4;s:5:"price";i:5;s:10:"cost_price";i:6;s:11:"category_id";i:7;s:14:"stock_quantity";i:8;s:9:"is_active";i:9;s:12:"is_food_item";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:8:" * dates";a:1:{i:0;s:10:"deleted_at";}s:16:" * forceDeleting";b:0;}i:6;O:18:"App\Models\Product":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:7;s:4:"name";s:11:"Ayam Goreng";s:3:"sku";s:10:"AYAMGO5313";s:5:"price";s:8:"20000.00";s:10:"cost_price";s:8:"16000.00";s:11:"category_id";i:1;s:14:"stock_quantity";i:20;s:12:"is_food_item";i:0;}s:11:" * original";a:8:{s:2:"id";i:7;s:4:"name";s:11:"Ayam Goreng";s:3:"sku";s:10:"AYAMGO5313";s:5:"price";s:8:"20000.00";s:10:"cost_price";s:8:"16000.00";s:11:"category_id";i:1;s:14:"stock_quantity";i:20;s:12:"is_food_item";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:5:"price";s:9:"decimal:2";s:10:"cost_price";s:9:"decimal:2";s:14:"stock_quantity";s:7:"integer";s:9:"is_active";s:7:"boolean";s:12:"is_food_item";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:50;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:10:{i:0;s:4:"name";i:1;s:3:"sku";i:2;s:7:"barcode";i:3;s:11:"description";i:4;s:5:"price";i:5;s:10:"cost_price";i:6;s:11:"category_id";i:7;s:14:"stock_quantity";i:8;s:9:"is_active";i:9;s:12:"is_food_item";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:8:" * dates";a:1:{i:0;s:10:"deleted_at";}s:16:" * forceDeleting";b:0;}i:7;O:18:"App\Models\Product":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:11;s:4:"name";s:10:"Ayam Gulai";s:3:"sku";s:10:"AYAMGU2396";s:5:"price";s:8:"20000.00";s:10:"cost_price";s:8:"17000.00";s:11:"category_id";i:1;s:14:"stock_quantity";i:20;s:12:"is_food_item";i:1;}s:11:" * original";a:8:{s:2:"id";i:11;s:4:"name";s:10:"Ayam Gulai";s:3:"sku";s:10:"AYAMGU2396";s:5:"price";s:8:"20000.00";s:10:"cost_price";s:8:"17000.00";s:11:"category_id";i:1;s:14:"stock_quantity";i:20;s:12:"is_food_item";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:5:"price";s:9:"decimal:2";s:10:"cost_price";s:9:"decimal:2";s:14:"stock_quantity";s:7:"integer";s:9:"is_active";s:7:"boolean";s:12:"is_food_item";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:50;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:10:{i:0;s:4:"name";i:1;s:3:"sku";i:2;s:7:"barcode";i:3;s:11:"description";i:4;s:5:"price";i:5;s:10:"cost_price";i:6;s:11:"category_id";i:7;s:14:"stock_quantity";i:8;s:9:"is_active";i:9;s:12:"is_food_item";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:8:" * dates";a:1:{i:0;s:10:"deleted_at";}s:16:" * forceDeleting";b:0;}i:8;O:18:"App\Models\Product":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:12;s:4:"name";s:10:"Ayam Bakar";s:3:"sku";s:10:"AYAMBA2027";s:5:"price";s:8:"25000.00";s:10:"cost_price";s:8:"17000.00";s:11:"category_id";i:1;s:14:"stock_quantity";i:20;s:12:"is_food_item";i:1;}s:11:" * original";a:8:{s:2:"id";i:12;s:4:"name";s:10:"Ayam Bakar";s:3:"sku";s:10:"AYAMBA2027";s:5:"price";s:8:"25000.00";s:10:"cost_price";s:8:"17000.00";s:11:"category_id";i:1;s:14:"stock_quantity";i:20;s:12:"is_food_item";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:5:"price";s:9:"decimal:2";s:10:"cost_price";s:9:"decimal:2";s:14:"stock_quantity";s:7:"integer";s:9:"is_active";s:7:"boolean";s:12:"is_food_item";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:50;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:10:{i:0;s:4:"name";i:1;s:3:"sku";i:2;s:7:"barcode";i:3;s:11:"description";i:4;s:5:"price";i:5;s:10:"cost_price";i:6;s:11:"category_id";i:7;s:14:"stock_quantity";i:8;s:9:"is_active";i:9;s:12:"is_food_item";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:8:" * dates";a:1:{i:0;s:10:"deleted_at";}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}s:11:"total_count";i:9;s:12:"last_updated";s:27:"2025-07-31T02:39:01.174688Z";}