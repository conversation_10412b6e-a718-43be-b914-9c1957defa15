<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Price List Info -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">
                        {{ $this->getRecord()->name }}
                    </h2>
                    <p class="text-sm text-gray-600">
                        Code: {{ $this->getRecord()->code }}
                    </p>
                    @if($this->getRecord()->description)
                        <p class="text-sm text-gray-600 mt-1">
                            {{ $this->getRecord()->description }}
                        </p>
                    @endif
                </div>
                <div class="text-right">
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Outlet-Specific
                        </span>
                        
                        @if($this->getRecord()->is_active)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Active
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Inactive
                            </span>
                        @endif
                    </div>
                    <p class="text-sm text-gray-600 mt-2">
                        {{ $this->getRecord()->activeOutlets()->count() }} outlets assigned
                    </p>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">
                        How to assign this price list to outlets:
                    </h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Use the "Assign" or "Unassign" buttons to include/exclude outlets</li>
                            <li>Set priority for outlets with multiple price lists (1 = highest priority)</li>
                            <li>Use "Edit Assignment" to configure effective dates and priority</li>
                            <li>Use bulk actions to assign or unassign multiple outlets at once</li>
                            <li>Use "Assign to All Outlets" to quickly assign to all active outlets</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Priority Information -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">
                        Priority System
                    </h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>
                            When an outlet has multiple price lists assigned, the system will use the price list with the highest priority (lowest number). 
                            If a product is not found in the highest priority price list, it will check the next priority, and so on. 
                            If no price is found in any assigned price list, it will fall back to the global price list.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outlets Table -->
        <div class="bg-white rounded-lg shadow">
            {{ $this->table }}
        </div>
    </div>
</x-filament-panels::page>
