<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PriceList;
use App\Models\Product;
use App\Models\PriceListItem;

class PriceListSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Global Price List
        $globalPriceList = PriceList::create([
            'name' => 'Global Price List',
            'code' => 'GLOBAL',
            'description' => 'Default global price list for all outlets',
            'is_global' => true,
            'is_active' => true,
            'effective_from' => now()->toDateString(),
            'created_by' => 1, // Assuming admin user has ID 1
        ]);

        // Add all existing products to global price list with their current prices
        $products = Product::where('is_active', true)->get();
        
        foreach ($products as $product) {
            PriceListItem::create([
                'price_list_id' => $globalPriceList->id,
                'product_id' => $product->id,
                'price' => $product->price,
                'cost_price' => $product->cost_price,
                'is_active' => true,
            ]);
        }

        $this->command->info('Global price list created with ' . $products->count() . ' products');
    }
}
