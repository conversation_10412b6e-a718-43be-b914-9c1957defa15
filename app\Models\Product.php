<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'sku',
        'barcode',
        'description',
        'price',
        'cost_price',
        'category_id',
        'stock_quantity',
        'is_active',
        'is_food_item',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'stock_quantity' => 'integer',
        'is_active' => 'boolean',
        'is_food_item' => 'boolean',
    ];

    protected $dates = ['deleted_at'];

    /**
     * Relasi ke Category
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Relasi ke QuotationItem
     */
    public function quotationItems()
    {
        return $this->hasMany(QuotationItem::class);
    }

    /**
     * <PERSON>lasi ke SalesOrderItem
     */
    public function salesOrderItems()
    {
        return $this->hasMany(SalesOrderItem::class);
    }

    /**
     * Relasi ke PriceListItem
     */
    public function priceListItems()
    {
        return $this->hasMany(PriceListItem::class);
    }

    /**
     * Get price lists that contain this product
     */
    public function priceLists()
    {
        return $this->belongsToMany(PriceList::class, 'price_list_items')
            ->withPivot(['price', 'cost_price', 'is_active'])
            ->withTimestamps();
    }

    /**
     * Get price for a specific outlet
     */
    public function getPriceForOutlet($outletId)
    {
        // Get outlet's assigned price lists ordered by priority
        $outlet = Outlet::find($outletId);
        if (!$outlet) {
            return $this->price; // fallback to product's default price
        }

        $priceLists = $outlet->activePriceLists()->orderBy('outlet_price_lists.priority')->get();

        // Check each price list for this product
        foreach ($priceLists as $priceList) {
            $priceListItem = $priceList->activeItems()->where('product_id', $this->id)->first();
            if ($priceListItem) {
                return $priceListItem->price;
            }
        }

        // If no specific price found, try global price list
        $globalPriceList = PriceList::getGlobal();
        if ($globalPriceList) {
            $globalPrice = $globalPriceList->getPriceForProduct($this->id);
            if ($globalPrice) {
                return $globalPrice;
            }
        }

        // Final fallback to product's default price
        return $this->price;
    }

    /**
     * Get cost price for a specific outlet
     */
    public function getCostPriceForOutlet($outletId)
    {
        // Get outlet's assigned price lists ordered by priority
        $outlet = Outlet::find($outletId);
        if (!$outlet) {
            return $this->cost_price; // fallback to product's default cost price
        }

        $priceLists = $outlet->activePriceLists()->orderBy('outlet_price_lists.priority')->get();

        // Check each price list for this product
        foreach ($priceLists as $priceList) {
            $priceListItem = $priceList->activeItems()->where('product_id', $this->id)->first();
            if ($priceListItem && $priceListItem->cost_price) {
                return $priceListItem->cost_price;
            }
        }

        // If no specific cost price found, try global price list
        $globalPriceList = PriceList::getGlobal();
        if ($globalPriceList) {
            $globalItem = $globalPriceList->activeItems()->where('product_id', $this->id)->first();
            if ($globalItem && $globalItem->cost_price) {
                return $globalItem->cost_price;
            }
        }

        // Final fallback to product's default cost price
        return $this->cost_price;
    }

    /**
     * Relasi ke PosTransactionItem
     */
    public function posTransactionItems()
    {
        return $this->hasMany(PosTransactionItem::class);
    }

    /**
     * Get profit margin
     */
    public function getProfitMarginAttribute(): float
    {
        if (!$this->cost_price || $this->cost_price == 0) {
            return 0;
        }

        return (($this->price - $this->cost_price) / $this->cost_price) * 100;
    }

    /**
     * Get profit amount
     */
    public function getProfitAmountAttribute(): float
    {
        return $this->price - ($this->cost_price ?? 0);
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        return 'Rp ' . number_format($this->price, 0, ',', '.');
    }

    /**
     * Get formatted cost price
     */
    public function getFormattedCostPriceAttribute(): string
    {
        return 'Rp ' . number_format($this->cost_price ?? 0, 0, ',', '.');
    }

    /**
     * Scope untuk produk aktif
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope untuk produk tidak aktif
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope untuk produk makanan
     */
    public function scopeFoodItems($query)
    {
        return $query->where('is_food_item', true);
    }

    /**
     * Scope untuk produk non-makanan
     */
    public function scopeNonFoodItems($query)
    {
        return $query->where('is_food_item', false);
    }

    /**
     * Scope untuk produk dengan stok rendah
     */
    public function scopeLowStock($query, $threshold = 10)
    {
        return $query->where('stock_quantity', '<=', $threshold);
    }

    /**
     * Scope untuk produk tanpa stok
     */
    public function scopeOutOfStock($query)
    {
        return $query->where('stock_quantity', 0);
    }

    /**
     * Check if product is low stock
     */
    public function isLowStock($threshold = 10): bool
    {
        return $this->stock_quantity <= $threshold;
    }

    /**
     * Check if product is out of stock
     */
    public function isOutOfStock(): bool
    {
        return $this->stock_quantity == 0;
    }
}
