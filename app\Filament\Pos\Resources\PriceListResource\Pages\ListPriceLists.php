<?php

namespace App\Filament\Pos\Resources\PriceListResource\Pages;

use App\Filament\Pos\Resources\PriceListResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\Action;
use App\Models\PriceList;
use App\Models\Product;
use Filament\Notifications\Notification;

class ListPriceLists extends ListRecords
{
    protected static string $resource = PriceListResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('create_global')
                ->label('Create Global Price List')
                ->icon('heroicon-o-globe-alt')
                ->color('warning')
                ->visible(fn () => !PriceList::where('is_global', true)->where('is_active', true)->exists())
                ->action(function () {
                    $globalPriceList = PriceList::create([
                        'name' => 'Global Price List',
                        'code' => 'GLOBAL_' . now()->format('Ymd_His'),
                        'description' => 'Default global price list for all outlets',
                        'is_global' => true,
                        'is_active' => true,
                        'effective_from' => now()->toDateString(),
                        'created_by' => auth()->id(),
                    ]);

                    // Add all active products to global price list
                    $products = Product::where('is_active', true)->get();
                    
                    foreach ($products as $product) {
                        $globalPriceList->addProduct(
                            $product->id,
                            $product->price,
                            $product->cost_price
                        );
                    }

                    Notification::make()
                        ->title('Global Price List Created')
                        ->body("Created global price list with {$products->count()} products")
                        ->success()
                        ->send();

                    return redirect()->route('filament.pos.resources.price-lists.edit', $globalPriceList);
                }),

            Actions\CreateAction::make()
                ->label('Create Price List'),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // Add widgets here if needed
        ];
    }
}
