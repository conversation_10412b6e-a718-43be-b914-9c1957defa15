<?php

namespace App\Filament\Pos\Resources\PriceListResource\Pages;

use App\Filament\Pos\Resources\PriceListResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Actions\Action;
use Filament\Notifications\Notification;

class EditPriceList extends EditRecord
{
    protected static string $resource = PriceListResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('manage_products')
                ->label('Manage Products')
                ->icon('heroicon-o-cube')
                ->url(fn (): string => route('filament.pos.resources.price-lists.manage-products', $this->getRecord())),

            Action::make('assign_outlets')
                ->label('Assign to Outlets')
                ->icon('heroicon-o-building-storefront')
                ->url(fn (): string => route('filament.pos.resources.price-lists.assign-outlets', $this->getRecord()))
                ->visible(fn (): bool => !$this->getRecord()->is_global),

            Action::make('duplicate')
                ->label('Duplicate')
                ->icon('heroicon-o-document-duplicate')
                ->color('gray')
                ->action(function () {
                    $original = $this->getRecord();
                    
                    $duplicate = $original->replicate();
                    $duplicate->name = $original->name . ' (Copy)';
                    $duplicate->code = $original->code . '_COPY_' . now()->format('His');
                    $duplicate->is_global = false; // Duplicates are never global
                    $duplicate->created_by = auth()->id();
                    $duplicate->save();

                    // Copy all price list items
                    foreach ($original->items as $item) {
                        $duplicateItem = $item->replicate();
                        $duplicateItem->price_list_id = $duplicate->id;
                        $duplicateItem->save();
                    }

                    Notification::make()
                        ->success()
                        ->title('Price List Duplicated')
                        ->body("Created '{$duplicate->name}' with {$original->items->count()} products")
                        ->send();

                    return redirect()->route('filament.pos.resources.price-lists.edit', $duplicate);
                }),

            Actions\DeleteAction::make()
                ->before(function () {
                    $record = $this->getRecord();
                    
                    // Prevent deletion of global price list if it's the only one
                    if ($record->is_global && \App\Models\PriceList::where('is_global', true)->count() === 1) {
                        Notification::make()
                            ->danger()
                            ->title('Cannot Delete')
                            ->body('Cannot delete the only global price list.')
                            ->send();
                            
                        $this->halt();
                    }
                }),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Prevent changing global status if this is the only global price list
        $record = $this->getRecord();
        
        if ($record->is_global && !($data['is_global'] ?? true)) {
            $globalCount = \App\Models\PriceList::where('is_global', true)->count();
            
            if ($globalCount === 1) {
                $data['is_global'] = true;
                
                Notification::make()
                    ->warning()
                    ->title('Cannot Change Global Status')
                    ->body('Cannot remove global status from the only global price list.')
                    ->send();
            }
        }

        return $data;
    }
}
