<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Product;
use App\Models\Outlet;
use App\Models\PriceList;
use App\Services\PriceResolutionService;
use Illuminate\Validation\ValidationException;

class PosPriceController extends Controller
{
    protected $priceResolutionService;

    public function __construct(PriceResolutionService $priceResolutionService)
    {
        $this->priceResolutionService = $priceResolutionService;
    }

    /**
     * Get product prices for a specific outlet
     */
    public function getProductPrices(Request $request)
    {
        $request->validate([
            'outlet_id' => 'required|exists:outlets,id',
            'product_ids' => 'nullable|array',
            'product_ids.*' => 'exists:products,id',
            'category_id' => 'nullable|exists:categories,id',
            'search' => 'nullable|string|max:255',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $outletId = $request->outlet_id;
        $productIds = $request->product_ids;
        $categoryId = $request->category_id;
        $search = $request->search;
        $perPage = $request->per_page ?? 50;

        // Build products query
        $query = Product::where('is_active', true);

        if ($productIds) {
            $query->whereIn('id', $productIds);
        }

        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%");
            });
        }

        $products = $query->with(['category'])->paginate($perPage);

        // Get prices for each product
        $productsWithPrices = $products->map(function ($product) use ($outletId) {
            $priceInfo = $this->priceResolutionService->getProductPrice($product->id, $outletId);
            
            return [
                'id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'barcode' => $product->barcode,
                'description' => $product->description,
                'category' => [
                    'id' => $product->category->id,
                    'name' => $product->category->name,
                ],
                'default_price' => (float) $product->price,
                'default_cost_price' => (float) $product->cost_price,
                'outlet_price' => (float) $priceInfo['price'],
                'outlet_cost_price' => (float) $priceInfo['cost_price'],
                'price_source' => $priceInfo['source'],
                'price_list_id' => $priceInfo['price_list_id'],
                'price_list_name' => $priceInfo['price_list_name'],
                'stock_quantity' => $product->stock_quantity,
                'is_food_item' => $product->is_food_item,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $productsWithPrices,
            'pagination' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
                'from' => $products->firstItem(),
                'to' => $products->lastItem(),
            ],
        ]);
    }

    /**
     * Get price for a single product at a specific outlet
     */
    public function getProductPrice(Request $request, $productId)
    {
        $request->validate([
            'outlet_id' => 'required|exists:outlets,id',
        ]);

        $product = Product::where('is_active', true)->findOrFail($productId);
        $outletId = $request->outlet_id;

        $priceInfo = $this->priceResolutionService->getProductPrice($productId, $outletId);

        return response()->json([
            'success' => true,
            'data' => [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'product_sku' => $product->sku,
                'default_price' => (float) $product->price,
                'default_cost_price' => (float) $product->cost_price,
                'outlet_price' => (float) $priceInfo['price'],
                'outlet_cost_price' => (float) $priceInfo['cost_price'],
                'price_source' => $priceInfo['source'],
                'price_list_id' => $priceInfo['price_list_id'],
                'price_list_name' => $priceInfo['price_list_name'],
                'price_difference' => (float) ($priceInfo['price'] - $product->price),
                'price_difference_percentage' => $product->price > 0 ? 
                    round((($priceInfo['price'] - $product->price) / $product->price) * 100, 2) : 0,
            ],
        ]);
    }

    /**
     * Get all price lists assigned to an outlet
     */
    public function getOutletPriceLists(Request $request, $outletId)
    {
        $outlet = Outlet::findOrFail($outletId);

        $priceLists = $outlet->activePriceLists()
            ->orderBy('outlet_price_lists.priority')
            ->get()
            ->map(function ($priceList) {
                return [
                    'id' => $priceList->id,
                    'name' => $priceList->name,
                    'code' => $priceList->code,
                    'description' => $priceList->description,
                    'is_global' => $priceList->is_global,
                    'priority' => $priceList->pivot->priority,
                    'effective_from' => $priceList->pivot->effective_from,
                    'effective_until' => $priceList->pivot->effective_until,
                    'products_count' => $priceList->activeItems()->count(),
                ];
            });

        // Add global price list if exists
        $globalPriceList = PriceList::getGlobal();
        if ($globalPriceList) {
            $priceLists->push([
                'id' => $globalPriceList->id,
                'name' => $globalPriceList->name,
                'code' => $globalPriceList->code,
                'description' => $globalPriceList->description,
                'is_global' => true,
                'priority' => 999, // Global always has lowest priority
                'effective_from' => $globalPriceList->effective_from,
                'effective_until' => $globalPriceList->effective_until,
                'products_count' => $globalPriceList->activeItems()->count(),
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'outlet' => [
                    'id' => $outlet->id,
                    'name' => $outlet->name,
                    'code' => $outlet->code,
                    'type' => $outlet->type,
                ],
                'price_lists' => $priceLists,
            ],
        ]);
    }

    /**
     * Get price comparison between outlets for a product
     */
    public function getProductPriceComparison(Request $request, $productId)
    {
        $request->validate([
            'outlet_ids' => 'nullable|array',
            'outlet_ids.*' => 'exists:outlets,id',
        ]);

        $product = Product::where('is_active', true)->findOrFail($productId);
        $outletIds = $request->outlet_ids;

        // If no outlet IDs provided, get all active outlets
        if (!$outletIds) {
            $outletIds = Outlet::where('is_active', true)->pluck('id')->toArray();
        }

        $priceComparison = [];

        foreach ($outletIds as $outletId) {
            $outlet = Outlet::find($outletId);
            if (!$outlet) continue;

            $priceInfo = $this->priceResolutionService->getProductPrice($productId, $outletId);

            $priceComparison[] = [
                'outlet_id' => $outlet->id,
                'outlet_name' => $outlet->name,
                'outlet_code' => $outlet->code,
                'outlet_type' => $outlet->type,
                'price' => (float) $priceInfo['price'],
                'cost_price' => (float) $priceInfo['cost_price'],
                'price_source' => $priceInfo['source'],
                'price_list_id' => $priceInfo['price_list_id'],
                'price_list_name' => $priceInfo['price_list_name'],
                'price_difference_from_default' => (float) ($priceInfo['price'] - $product->price),
            ];
        }

        // Sort by price
        usort($priceComparison, function ($a, $b) {
            return $a['price'] <=> $b['price'];
        });

        return response()->json([
            'success' => true,
            'data' => [
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'default_price' => (float) $product->price,
                ],
                'price_comparison' => $priceComparison,
                'price_range' => [
                    'min_price' => count($priceComparison) > 0 ? min(array_column($priceComparison, 'price')) : 0,
                    'max_price' => count($priceComparison) > 0 ? max(array_column($priceComparison, 'price')) : 0,
                ],
            ],
        ]);
    }

    /**
     * Bulk get product prices for multiple products and outlets
     */
    public function bulkGetProductPrices(Request $request)
    {
        $request->validate([
            'items' => 'required|array|max:100',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.outlet_id' => 'required|exists:outlets,id',
        ]);

        $results = [];

        foreach ($request->items as $item) {
            $productId = $item['product_id'];
            $outletId = $item['outlet_id'];

            $product = Product::find($productId);
            if (!$product || !$product->is_active) {
                continue;
            }

            $priceInfo = $this->priceResolutionService->getProductPrice($productId, $outletId);

            $results[] = [
                'product_id' => $productId,
                'outlet_id' => $outletId,
                'price' => (float) $priceInfo['price'],
                'cost_price' => (float) $priceInfo['cost_price'],
                'price_source' => $priceInfo['source'],
                'price_list_id' => $priceInfo['price_list_id'],
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $results,
        ]);
    }

    /**
     * Debug price resolution for a specific product and outlet
     */
    public function debugPriceResolution(Request $request, $productId, $outletId)
    {
        $summary = $this->priceResolutionService->getPriceResolutionSummary($productId, $outletId);

        return response()->json([
            'success' => true,
            'data' => $summary,
        ]);
    }

    /**
     * Validate outlet price configuration
     */
    public function validateOutletConfiguration(Request $request, $outletId)
    {
        $validation = $this->priceResolutionService->validateOutletPriceConfiguration($outletId);

        return response()->json([
            'success' => true,
            'data' => $validation,
        ]);
    }
}
