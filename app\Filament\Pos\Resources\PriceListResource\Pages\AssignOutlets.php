<?php

namespace App\Filament\Pos\Resources\PriceListResource\Pages;

use App\Filament\Pos\Resources\PriceListResource;
use Filament\Resources\Pages\Page;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables;
use Filament\Forms;
use App\Models\Outlet;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;

class AssignOutlets extends Page implements HasTable
{
    use InteractsWithTable;

    protected static string $resource = PriceListResource::class;

    protected static string $view = 'filament.pos.resources.price-list-resource.pages.assign-outlets';

    public function mount(): void
    {
        $this->record = $this->getRecord();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('assign_all_outlets')
                ->label('Assign to All Outlets')
                ->icon('heroicon-o-plus-circle')
                ->color('success')
                ->action(function () {
                    $priceList = $this->getRecord();
                    $assignedOutletIds = $priceList->outlets()->pluck('outlets.id')->toArray();
                    
                    $outlets = Outlet::where('is_active', true)
                        ->whereNotIn('id', $assignedOutletIds)
                        ->get();

                    $assigned = 0;
                    foreach ($outlets as $outlet) {
                        $outlet->assignPriceList($priceList->id, 1);
                        $assigned++;
                    }

                    Notification::make()
                        ->success()
                        ->title('Outlets Assigned')
                        ->body("Assigned price list to {$assigned} outlets")
                        ->send();
                }),

            Action::make('back')
                ->label('Back to Price Lists')
                ->icon('heroicon-o-arrow-left')
                ->color('gray')
                ->url(fn (): string => route('filament.pos.resources.price-lists.index')),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Outlet::query()
                    ->leftJoin('outlet_price_lists', function ($join) {
                        $join->on('outlets.id', '=', 'outlet_price_lists.outlet_id')
                             ->where('outlet_price_lists.price_list_id', $this->getRecord()->id);
                    })
                    ->select([
                        'outlets.*',
                        'outlet_price_lists.id as assignment_id',
                        'outlet_price_lists.priority',
                        'outlet_price_lists.is_active as is_assigned',
                        'outlet_price_lists.effective_from',
                        'outlet_price_lists.effective_until'
                    ])
            )
            ->columns([
                TextColumn::make('name')
                    ->label('Outlet Name')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('code')
                    ->label('Code')
                    ->searchable(),

                TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->sortable(),

                TextColumn::make('city')
                    ->label('City')
                    ->searchable()
                    ->sortable(),

                BooleanColumn::make('is_assigned')
                    ->label('Assigned')
                    ->sortable(),

                TextInputColumn::make('priority')
                    ->label('Priority')
                    ->type('number')
                    ->rules(['integer', 'min:1', 'max:10'])
                    ->placeholder('1')
                    ->beforeStateUpdated(function ($record, $state) {
                        if ($record->is_assigned && $state) {
                            \DB::table('outlet_price_lists')
                                ->where('id', $record->assignment_id)
                                ->update(['priority' => $state]);
                        }
                    })
                    ->visible(fn ($record) => $record->is_assigned),

                TextColumn::make('effective_from')
                    ->label('Effective From')
                    ->date()
                    ->placeholder('Immediate')
                    ->visible(fn ($record) => $record->is_assigned),

                TextColumn::make('effective_until')
                    ->label('Effective Until')
                    ->date()
                    ->placeholder('No expiration')
                    ->visible(fn ($record) => $record->is_assigned),

                BooleanColumn::make('is_active')
                    ->label('Outlet Active')
                    ->sortable(),
            ])
            ->filters([
                TernaryFilter::make('is_assigned')
                    ->label('Assignment Status')
                    ->placeholder('All outlets')
                    ->trueLabel('Assigned only')
                    ->falseLabel('Not assigned only'),

                SelectFilter::make('type')
                    ->label('Outlet Type')
                    ->options([
                        'toko' => 'Toko',
                        'restoran' => 'Restoran',
                        'kafe' => 'Kafe',
                        'minimarket' => 'Minimarket',
                        'supermarket' => 'Supermarket',
                    ]),

                TernaryFilter::make('is_active')
                    ->label('Outlet Status')
                    ->placeholder('All outlets')
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only'),
            ])
            ->actions([
                Tables\Actions\Action::make('toggle_assignment')
                    ->label(fn ($record) => $record->is_assigned ? 'Unassign' : 'Assign')
                    ->icon(fn ($record) => $record->is_assigned ? 'heroicon-o-minus-circle' : 'heroicon-o-plus-circle')
                    ->color(fn ($record) => $record->is_assigned ? 'danger' : 'success')
                    ->action(function ($record) {
                        $priceList = $this->getRecord();
                        
                        if ($record->is_assigned) {
                            $record->removePriceList($priceList->id);
                            Notification::make()
                                ->success()
                                ->title('Price List Unassigned')
                                ->body("Unassigned price list from {$record->name}")
                                ->send();
                        } else {
                            $record->assignPriceList($priceList->id, 1);
                            Notification::make()
                                ->success()
                                ->title('Price List Assigned')
                                ->body("Assigned price list to {$record->name}")
                                ->send();
                        }
                    }),

                Tables\Actions\Action::make('edit_assignment')
                    ->label('Edit Assignment')
                    ->icon('heroicon-o-pencil')
                    ->color('warning')
                    ->visible(fn ($record) => $record->is_assigned)
                    ->form([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('priority')
                                    ->label('Priority')
                                    ->numeric()
                                    ->required()
                                    ->minValue(1)
                                    ->maxValue(10)
                                    ->helperText('1 = highest priority'),

                                Forms\Components\Toggle::make('is_active')
                                    ->label('Active Assignment')
                                    ->default(true),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('effective_from')
                                    ->label('Effective From')
                                    ->helperText('Leave empty for immediate effect'),

                                Forms\Components\DatePicker::make('effective_until')
                                    ->label('Effective Until')
                                    ->helperText('Leave empty for no expiration'),
                            ]),
                    ])
                    ->fillForm(function ($record) {
                        return [
                            'priority' => $record->priority ?: 1,
                            'is_active' => $record->is_assigned,
                            'effective_from' => $record->effective_from,
                            'effective_until' => $record->effective_until,
                        ];
                    })
                    ->action(function ($record, array $data) {
                        \DB::table('outlet_price_lists')
                            ->where('id', $record->assignment_id)
                            ->update([
                                'priority' => $data['priority'],
                                'is_active' => $data['is_active'],
                                'effective_from' => $data['effective_from'],
                                'effective_until' => $data['effective_until'],
                                'updated_at' => now(),
                            ]);

                        Notification::make()
                            ->success()
                            ->title('Assignment Updated')
                            ->body("Updated assignment for {$record->name}")
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('assign_to_outlets')
                    ->label('Assign to Outlets')
                    ->icon('heroicon-o-plus-circle')
                    ->color('success')
                    ->action(function ($records) {
                        $priceList = $this->getRecord();
                        $assigned = 0;
                        
                        foreach ($records as $record) {
                            if (!$record->is_assigned) {
                                $record->assignPriceList($priceList->id, 1);
                                $assigned++;
                            }
                        }

                        Notification::make()
                            ->success()
                            ->title('Price List Assigned')
                            ->body("Assigned price list to {$assigned} outlets")
                            ->send();
                    }),

                Tables\Actions\BulkAction::make('unassign_from_outlets')
                    ->label('Unassign from Outlets')
                    ->icon('heroicon-o-minus-circle')
                    ->color('danger')
                    ->action(function ($records) {
                        $priceList = $this->getRecord();
                        $unassigned = 0;
                        
                        foreach ($records as $record) {
                            if ($record->is_assigned) {
                                $record->removePriceList($priceList->id);
                                $unassigned++;
                            }
                        }

                        Notification::make()
                            ->success()
                            ->title('Price List Unassigned')
                            ->body("Unassigned price list from {$unassigned} outlets")
                            ->send();
                    }),
            ])
            ->defaultSort('name');
    }
}
