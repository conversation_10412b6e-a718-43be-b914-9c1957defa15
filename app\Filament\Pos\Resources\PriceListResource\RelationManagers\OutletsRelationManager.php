<?php

namespace App\Filament\Pos\Resources\PriceListResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;

class OutletsRelationManager extends RelationManager
{
    protected static string $relationship = 'outlets';

    protected static ?string $title = 'Assigned Outlets';

    protected static ?string $modelLabel = 'Outlet';

    protected static ?string $pluralModelLabel = 'Outlets';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('outlet_id')
                    ->label('Outlet')
                    ->relationship('outlet', 'name')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->disabled(fn ($context) => $context === 'edit'),

                Grid::make(2)
                    ->schema([
                        TextInput::make('priority')
                            ->label('Priority')
                            ->numeric()
                            ->required()
                            ->minValue(1)
                            ->maxValue(10)
                            ->default(1)
                            ->helperText('1 = highest priority'),

                        Toggle::make('is_active')
                            ->label('Active Assignment')
                            ->default(true),
                    ]),

                Grid::make(2)
                    ->schema([
                        DatePicker::make('effective_from')
                            ->label('Effective From')
                            ->helperText('Leave empty for immediate effect'),

                        DatePicker::make('effective_until')
                            ->label('Effective Until')
                            ->helperText('Leave empty for no expiration'),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                TextColumn::make('name')
                    ->label('Outlet Name')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('code')
                    ->label('Code')
                    ->searchable(),

                TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->sortable(),

                TextColumn::make('city')
                    ->label('City')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('pivot.priority')
                    ->label('Priority')
                    ->sortable()
                    ->badge()
                    ->color(function ($state) {
                        return match ($state) {
                            1 => 'success',
                            2, 3 => 'warning',
                            default => 'gray',
                        };
                    }),

                BooleanColumn::make('pivot.is_active')
                    ->label('Active Assignment')
                    ->sortable(),

                TextColumn::make('pivot.effective_from')
                    ->label('Effective From')
                    ->date()
                    ->sortable()
                    ->placeholder('Immediate'),

                TextColumn::make('pivot.effective_until')
                    ->label('Effective Until')
                    ->date()
                    ->sortable()
                    ->placeholder('No expiration'),

                BooleanColumn::make('is_active')
                    ->label('Outlet Active')
                    ->sortable(),

                TextColumn::make('pivot.created_at')
                    ->label('Assigned At')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->label('Outlet Type')
                    ->options([
                        'toko' => 'Toko',
                        'restoran' => 'Restoran',
                        'kafe' => 'Kafe',
                        'minimarket' => 'Minimarket',
                        'supermarket' => 'Supermarket',
                    ]),

                TernaryFilter::make('pivot.is_active')
                    ->label('Assignment Status')
                    ->placeholder('All assignments')
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only'),

                TernaryFilter::make('is_active')
                    ->label('Outlet Status')
                    ->placeholder('All outlets')
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only'),

                Tables\Filters\Filter::make('effective_now')
                    ->label('Currently Effective')
                    ->query(function (Builder $query): Builder {
                        $today = now()->toDateString();
                        return $query->where(function ($q) use ($today) {
                            $q->whereNull('outlet_price_lists.effective_from')
                              ->orWhere('outlet_price_lists.effective_from', '<=', $today);
                        })->where(function ($q) use ($today) {
                            $q->whereNull('outlet_price_lists.effective_until')
                              ->orWhere('outlet_price_lists.effective_until', '>=', $today);
                        });
                    }),
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->label('Assign Outlet')
                    ->form(fn (Tables\Actions\AttachAction $action): array => [
                        $action->getRecordSelect()
                            ->searchable()
                            ->preload(),
                        
                        Grid::make(2)
                            ->schema([
                                TextInput::make('priority')
                                    ->label('Priority')
                                    ->numeric()
                                    ->required()
                                    ->minValue(1)
                                    ->maxValue(10)
                                    ->default(1)
                                    ->helperText('1 = highest priority'),

                                Toggle::make('is_active')
                                    ->label('Active Assignment')
                                    ->default(true),
                            ]),

                        Grid::make(2)
                            ->schema([
                                DatePicker::make('effective_from')
                                    ->label('Effective From')
                                    ->helperText('Leave empty for immediate effect'),

                                DatePicker::make('effective_until')
                                    ->label('Effective Until')
                                    ->helperText('Leave empty for no expiration'),
                            ]),
                    ])
                    ->preloadRecordSelect(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->form([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('priority')
                                    ->label('Priority')
                                    ->numeric()
                                    ->required()
                                    ->minValue(1)
                                    ->maxValue(10)
                                    ->helperText('1 = highest priority'),

                                Toggle::make('is_active')
                                    ->label('Active Assignment'),
                            ]),

                        Grid::make(2)
                            ->schema([
                                DatePicker::make('effective_from')
                                    ->label('Effective From')
                                    ->helperText('Leave empty for immediate effect'),

                                DatePicker::make('effective_until')
                                    ->label('Effective Until')
                                    ->helperText('Leave empty for no expiration'),
                            ]),
                    ]),
                
                Tables\Actions\DetachAction::make()
                    ->label('Unassign'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make()
                        ->label('Unassign Selected'),
                    
                    Tables\Actions\BulkAction::make('update_priority')
                        ->label('Update Priority')
                        ->icon('heroicon-o-adjustments-horizontal')
                        ->color('warning')
                        ->form([
                            TextInput::make('priority')
                                ->label('New Priority')
                                ->numeric()
                                ->required()
                                ->minValue(1)
                                ->maxValue(10)
                                ->helperText('1 = highest priority'),
                        ])
                        ->action(function (array $data, $records) {
                            foreach ($records as $record) {
                                $record->pivot->update([
                                    'priority' => $data['priority'],
                                ]);
                            }
                        }),

                    Tables\Actions\BulkAction::make('toggle_active')
                        ->label('Toggle Active Status')
                        ->icon('heroicon-o-eye')
                        ->color('gray')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                $record->pivot->update([
                                    'is_active' => !$record->pivot->is_active,
                                ]);
                            }
                        }),
                ]),
            ])
            ->defaultSort('pivot.priority');
    }
}
