<?php

namespace App\Filament\Pos\Resources\PriceListResource\Pages;

use App\Filament\Pos\Resources\PriceListResource;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreatePriceList extends CreateRecord
{
    protected static string $resource = PriceListResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('manage-products', ['record' => $this->getRecord()]);
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Price List Created')
            ->body('The price list has been created successfully. You can now add products to it.');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = auth()->id();
        
        // Ensure only one global price list exists
        if ($data['is_global'] ?? false) {
            $existingGlobal = \App\Models\PriceList::where('is_global', true)
                ->where('is_active', true)
                ->first();
                
            if ($existingGlobal) {
                $data['is_global'] = false;
                
                Notification::make()
                    ->warning()
                    ->title('Global Price List Already Exists')
                    ->body('A global price list already exists. This price list will be created as outlet-specific.')
                    ->send();
            }
        }

        return $data;
    }
}
