<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PriceListItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'price_list_id',
        'product_id',
        'price',
        'cost_price',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the price list that owns this item
     */
    public function priceList(): BelongsTo
    {
        return $this->belongsTo(PriceList::class);
    }

    /**
     * Get the product for this price list item
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Scope to get only active items
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the profit margin for this item
     */
    public function getProfitMarginAttribute()
    {
        if (!$this->cost_price || $this->cost_price <= 0) {
            return null;
        }

        return (($this->price - $this->cost_price) / $this->cost_price) * 100;
    }

    /**
     * Get the profit amount for this item
     */
    public function getProfitAmountAttribute()
    {
        if (!$this->cost_price) {
            return null;
        }

        return $this->price - $this->cost_price;
    }

    /**
     * Check if this item has a different price than the global price list
     */
    public function isDifferentFromGlobal(): bool
    {
        $globalPriceList = PriceList::getGlobal();
        
        if (!$globalPriceList) {
            return false;
        }

        $globalPrice = $globalPriceList->getPriceForProduct($this->product_id);
        
        return $globalPrice && $globalPrice != $this->price;
    }

    /**
     * Get the price difference from global price list
     */
    public function getPriceDifferenceFromGlobal()
    {
        $globalPriceList = PriceList::getGlobal();
        
        if (!$globalPriceList) {
            return null;
        }

        $globalPrice = $globalPriceList->getPriceForProduct($this->product_id);
        
        if (!$globalPrice) {
            return null;
        }

        return $this->price - $globalPrice;
    }

    /**
     * Get the percentage difference from global price list
     */
    public function getPercentageDifferenceFromGlobal()
    {
        $globalPriceList = PriceList::getGlobal();
        
        if (!$globalPriceList) {
            return null;
        }

        $globalPrice = $globalPriceList->getPriceForProduct($this->product_id);
        
        if (!$globalPrice || $globalPrice <= 0) {
            return null;
        }

        return (($this->price - $globalPrice) / $globalPrice) * 100;
    }
}
