<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PriceList extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'description',
        'is_global',
        'is_active',
        'effective_from',
        'effective_until',
        'created_by',
    ];

    protected $casts = [
        'is_global' => 'boolean',
        'is_active' => 'boolean',
        'effective_from' => 'date',
        'effective_until' => 'date',
    ];

    protected $dates = ['deleted_at'];

    /**
     * Get the price list items for this price list
     */
    public function items(): HasMany
    {
        return $this->hasMany(PriceListItem::class);
    }

    /**
     * Get the active price list items for this price list
     */
    public function activeItems(): HasMany
    {
        return $this->hasMany(PriceListItem::class)->where('is_active', true);
    }

    /**
     * Get the outlets that use this price list
     */
    public function outlets(): BelongsToMany
    {
        return $this->belongsToMany(Outlet::class, 'outlet_price_lists')
            ->withPivot(['priority', 'is_active', 'effective_from', 'effective_until'])
            ->withTimestamps();
    }

    /**
     * Get the active outlets that use this price list
     */
    public function activeOutlets(): BelongsToMany
    {
        return $this->belongsToMany(Outlet::class, 'outlet_price_lists')
            ->withPivot(['priority', 'is_active', 'effective_from', 'effective_until'])
            ->wherePivot('is_active', true)
            ->withTimestamps();
    }

    /**
     * Get the user who created this price list
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to get only global price lists
     */
    public function scopeGlobal($query)
    {
        return $query->where('is_global', true);
    }

    /**
     * Scope to get only active price lists
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get effective price lists for a given date
     */
    public function scopeEffective($query, $date = null)
    {
        $date = $date ?: now()->toDateString();
        
        return $query->where(function ($q) use ($date) {
            $q->whereNull('effective_from')
              ->orWhere('effective_from', '<=', $date);
        })->where(function ($q) use ($date) {
            $q->whereNull('effective_until')
              ->orWhere('effective_until', '>=', $date);
        });
    }

    /**
     * Get the global price list
     */
    public static function getGlobal()
    {
        return static::where('is_global', true)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Check if this price list is currently effective
     */
    public function isEffective($date = null): bool
    {
        $date = $date ?: now()->toDateString();
        
        $fromCheck = is_null($this->effective_from) || $this->effective_from <= $date;
        $untilCheck = is_null($this->effective_until) || $this->effective_until >= $date;
        
        return $this->is_active && $fromCheck && $untilCheck;
    }

    /**
     * Get price for a specific product
     */
    public function getPriceForProduct($productId)
    {
        $item = $this->activeItems()->where('product_id', $productId)->first();
        return $item ? $item->price : null;
    }

    /**
     * Add product to this price list
     */
    public function addProduct($productId, $price, $costPrice = null)
    {
        return $this->items()->updateOrCreate(
            ['product_id' => $productId],
            [
                'price' => $price,
                'cost_price' => $costPrice,
                'is_active' => true,
            ]
        );
    }

    /**
     * Remove product from this price list
     */
    public function removeProduct($productId)
    {
        return $this->items()->where('product_id', $productId)->delete();
    }

    /**
     * Get products count in this price list
     */
    public function getProductsCountAttribute()
    {
        return $this->activeItems()->count();
    }

    /**
     * Get outlets count using this price list
     */
    public function getOutletsCountAttribute()
    {
        return $this->activeOutlets()->count();
    }
}
