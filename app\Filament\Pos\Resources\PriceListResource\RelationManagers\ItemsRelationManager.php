<?php

namespace App\Filament\Pos\Resources\PriceListResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;

class ItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'items';

    protected static ?string $title = 'Price List Items';

    protected static ?string $modelLabel = 'Item';

    protected static ?string $pluralModelLabel = 'Items';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('product_id')
                    ->label('Product')
                    ->relationship('product', 'name')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->createOptionForm([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('sku')
                            ->label('SKU')
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description'),
                        Forms\Components\TextInput::make('price')
                            ->label('Default Price')
                            ->numeric()
                            ->required(),
                        Forms\Components\TextInput::make('cost_price')
                            ->label('Cost Price')
                            ->numeric(),
                        Forms\Components\Select::make('category_id')
                            ->label('Category')
                            ->relationship('category', 'name')
                            ->required(),
                    ]),

                TextInput::make('price')
                    ->label('Price')
                    ->numeric()
                    ->required()
                    ->step(0.01)
                    ->minValue(0)
                    ->prefix('Rp'),

                TextInput::make('cost_price')
                    ->label('Cost Price')
                    ->numeric()
                    ->step(0.01)
                    ->minValue(0)
                    ->prefix('Rp'),

                Toggle::make('is_active')
                    ->label('Active')
                    ->default(true),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('product.name')
            ->columns([
                TextColumn::make('product.name')
                    ->label('Product')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('product.sku')
                    ->label('SKU')
                    ->searchable(),

                TextColumn::make('product.category.name')
                    ->label('Category')
                    ->sortable(),

                TextColumn::make('product.price')
                    ->label('Default Price')
                    ->money('IDR')
                    ->sortable(),

                TextColumn::make('price')
                    ->label('Price List Price')
                    ->money('IDR')
                    ->sortable(),

                TextColumn::make('cost_price')
                    ->label('Cost Price')
                    ->money('IDR')
                    ->sortable()
                    ->placeholder('Not set'),

                TextColumn::make('profit_margin')
                    ->label('Margin %')
                    ->getStateUsing(function ($record) {
                        if (!$record->cost_price || $record->cost_price <= 0) {
                            return null;
                        }
                        return round((($record->price - $record->cost_price) / $record->cost_price) * 100, 2) . '%';
                    })
                    ->placeholder('N/A'),

                TextColumn::make('price_difference_from_global')
                    ->label('Diff from Global')
                    ->getStateUsing(function ($record) {
                        $diff = $record->getPriceDifferenceFromGlobal();
                        if ($diff === null) {
                            return 'N/A';
                        }
                        return ($diff >= 0 ? '+' : '') . 'Rp ' . number_format($diff, 0, ',', '.');
                    })
                    ->color(function ($record) {
                        $diff = $record->getPriceDifferenceFromGlobal();
                        if ($diff === null) return 'gray';
                        return $diff > 0 ? 'success' : ($diff < 0 ? 'danger' : 'gray');
                    }),

                BooleanColumn::make('is_active')
                    ->label('Active')
                    ->sortable(),

                TextColumn::make('updated_at')
                    ->label('Last Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('product.category_id')
                    ->label('Category')
                    ->relationship('product.category', 'name'),

                TernaryFilter::make('is_active')
                    ->label('Status')
                    ->placeholder('All items')
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only'),

                Tables\Filters\Filter::make('has_cost_price')
                    ->label('Has Cost Price')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('cost_price')),

                Tables\Filters\Filter::make('different_from_global')
                    ->label('Different from Global')
                    ->query(function (Builder $query): Builder {
                        $globalPriceList = \App\Models\PriceList::getGlobal();
                        if (!$globalPriceList) {
                            return $query;
                        }

                        return $query->whereExists(function ($subQuery) use ($globalPriceList) {
                            $subQuery->select(\DB::raw(1))
                                ->from('price_list_items as global_items')
                                ->whereColumn('global_items.product_id', 'price_list_items.product_id')
                                ->where('global_items.price_list_id', $globalPriceList->id)
                                ->whereColumn('global_items.price', '!=', 'price_list_items.price');
                        });
                    }),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Add Product'),
                
                Tables\Actions\Action::make('bulk_add_products')
                    ->label('Bulk Add Products')
                    ->icon('heroicon-o-plus-circle')
                    ->color('success')
                    ->form([
                        Select::make('products')
                            ->label('Products')
                            ->multiple()
                            ->relationship('product', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),
                        
                        TextInput::make('price_multiplier')
                            ->label('Price Multiplier')
                            ->numeric()
                            ->step(0.01)
                            ->default(1)
                            ->helperText('Multiply default product price by this value'),
                    ])
                    ->action(function (array $data) {
                        $priceList = $this->getOwnerRecord();
                        $multiplier = $data['price_multiplier'] ?? 1;
                        
                        foreach ($data['products'] as $productId) {
                            $product = \App\Models\Product::find($productId);
                            if ($product) {
                                $priceList->addProduct(
                                    $product->id,
                                    $product->price * $multiplier,
                                    $product->cost_price
                                );
                            }
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    
                    Tables\Actions\BulkAction::make('update_prices')
                        ->label('Update Prices')
                        ->icon('heroicon-o-currency-dollar')
                        ->color('warning')
                        ->form([
                            TextInput::make('price_multiplier')
                                ->label('Price Multiplier')
                                ->numeric()
                                ->step(0.01)
                                ->required()
                                ->helperText('Multiply current prices by this value'),
                        ])
                        ->action(function (array $data, $records) {
                            $multiplier = $data['price_multiplier'];
                            
                            foreach ($records as $record) {
                                $record->update([
                                    'price' => $record->price * $multiplier,
                                ]);
                            }
                        }),

                    Tables\Actions\BulkAction::make('toggle_active')
                        ->label('Toggle Active Status')
                        ->icon('heroicon-o-eye')
                        ->color('gray')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                $record->update([
                                    'is_active' => !$record->is_active,
                                ]);
                            }
                        }),
                ]),
            ])
            ->defaultSort('product.name');
    }
}
