<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create price_lists table
        Schema::create('price_lists', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Nama price list');
            $table->string('code')->unique()->comment('Kode price list');
            $table->text('description')->nullable()->comment('Deskripsi price list');
            $table->boolean('is_global')->default(false)->comment('Apakah ini global price list');
            $table->boolean('is_active')->default(true)->comment('Status aktif');
            $table->date('effective_from')->nullable()->comment('Berlaku mulai tanggal');
            $table->date('effective_until')->nullable()->comment('Berlaku sampai tanggal');
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['is_global', 'is_active']);
            $table->index(['effective_from', 'effective_until']);
            $table->index(['code']);
        });

        // Create price_list_items table
        Schema::create('price_list_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('price_list_id')->constrained('price_lists')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->decimal('price', 15, 2)->comment('Harga produk dalam price list ini');
            $table->decimal('cost_price', 15, 2)->nullable()->comment('Harga beli/cost untuk price list ini');
            $table->boolean('is_active')->default(true)->comment('Status aktif item');
            $table->timestamps();

            // Unique constraint - satu produk hanya bisa ada sekali dalam satu price list
            $table->unique(['price_list_id', 'product_id']);
            
            // Indexes
            $table->index(['price_list_id', 'is_active']);
            $table->index(['product_id']);
        });

        // Create outlet_price_lists pivot table
        Schema::create('outlet_price_lists', function (Blueprint $table) {
            $table->id();
            $table->foreignId('outlet_id')->constrained('outlets')->onDelete('cascade');
            $table->foreignId('price_list_id')->constrained('price_lists')->onDelete('cascade');
            $table->integer('priority')->default(1)->comment('Prioritas price list (1 = tertinggi)');
            $table->boolean('is_active')->default(true)->comment('Status aktif assignment');
            $table->date('effective_from')->nullable()->comment('Berlaku mulai tanggal');
            $table->date('effective_until')->nullable()->comment('Berlaku sampai tanggal');
            $table->timestamps();

            // Unique constraint - satu outlet tidak bisa punya price list yang sama lebih dari sekali
            $table->unique(['outlet_id', 'price_list_id']);
            
            // Indexes
            $table->index(['outlet_id', 'is_active', 'priority']);
            $table->index(['price_list_id']);
            $table->index(['effective_from', 'effective_until']);
        });

        // Create user_outlets pivot table
        Schema::create('user_outlets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('outlet_id')->constrained('outlets')->onDelete('cascade');
            $table->enum('role', ['manager', 'supervisor', 'cashier', 'staff'])->default('staff')->comment('Role user di outlet');
            $table->boolean('is_active')->default(true)->comment('Status aktif assignment');
            $table->date('assigned_from')->nullable()->comment('Ditugaskan mulai tanggal');
            $table->date('assigned_until')->nullable()->comment('Ditugaskan sampai tanggal');
            $table->timestamps();

            // Unique constraint - satu user tidak bisa punya role yang sama di outlet yang sama
            $table->unique(['user_id', 'outlet_id', 'role']);
            
            // Indexes
            $table->index(['user_id', 'is_active']);
            $table->index(['outlet_id', 'is_active']);
            $table->index(['role']);
            $table->index(['assigned_from', 'assigned_until']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_outlets');
        Schema::dropIfExists('outlet_price_lists');
        Schema::dropIfExists('price_list_items');
        Schema::dropIfExists('price_lists');
    }
};
