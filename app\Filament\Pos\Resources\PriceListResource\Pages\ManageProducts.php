<?php

namespace App\Filament\Pos\Resources\PriceListResource\Pages;

use App\Filament\Pos\Resources\PriceListResource;
use Filament\Resources\Pages\Page;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables;
use Filament\Forms;
use App\Models\Product;
use App\Models\PriceListItem;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;

class ManageProducts extends Page implements HasTable
{
    use InteractsWithTable;

    protected static string $resource = PriceListResource::class;

    protected static string $view = 'filament.pos.resources.price-list-resource.pages.manage-products';

    public function mount(): void
    {
        $this->record = $this->getRecord();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('add_all_products')
                ->label('Add All Products')
                ->icon('heroicon-o-plus-circle')
                ->color('success')
                ->action(function () {
                    $priceList = $this->getRecord();
                    $existingProductIds = $priceList->items()->pluck('product_id')->toArray();
                    
                    $products = Product::where('is_active', true)
                        ->whereNotIn('id', $existingProductIds)
                        ->get();

                    $added = 0;
                    foreach ($products as $product) {
                        $priceList->addProduct(
                            $product->id,
                            $product->price,
                            $product->cost_price
                        );
                        $added++;
                    }

                    Notification::make()
                        ->success()
                        ->title('Products Added')
                        ->body("Added {$added} products to the price list")
                        ->send();
                }),

            Action::make('copy_from_global')
                ->label('Copy from Global')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('warning')
                ->visible(fn () => !$this->getRecord()->is_global)
                ->action(function () {
                    $priceList = $this->getRecord();
                    $globalPriceList = \App\Models\PriceList::getGlobal();
                    
                    if (!$globalPriceList) {
                        Notification::make()
                            ->danger()
                            ->title('No Global Price List')
                            ->body('No global price list found to copy from')
                            ->send();
                        return;
                    }

                    $copied = 0;
                    foreach ($globalPriceList->activeItems as $globalItem) {
                        $priceList->addProduct(
                            $globalItem->product_id,
                            $globalItem->price,
                            $globalItem->cost_price
                        );
                        $copied++;
                    }

                    Notification::make()
                        ->success()
                        ->title('Prices Copied')
                        ->body("Copied {$copied} products from global price list")
                        ->send();
                }),

            Action::make('back')
                ->label('Back to Price Lists')
                ->icon('heroicon-o-arrow-left')
                ->color('gray')
                ->url(fn (): string => route('filament.pos.resources.price-lists.index')),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Product::query()
                    ->leftJoin('price_list_items', function ($join) {
                        $join->on('products.id', '=', 'price_list_items.product_id')
                             ->where('price_list_items.price_list_id', $this->getRecord()->id);
                    })
                    ->select([
                        'products.*',
                        'price_list_items.id as price_list_item_id',
                        'price_list_items.price as price_list_price',
                        'price_list_items.cost_price as price_list_cost_price',
                        'price_list_items.is_active as in_price_list'
                    ])
            )
            ->columns([
                TextColumn::make('name')
                    ->label('Product Name')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('sku')
                    ->label('SKU')
                    ->searchable(),

                TextColumn::make('category.name')
                    ->label('Category')
                    ->sortable(),

                TextColumn::make('price')
                    ->label('Default Price')
                    ->money('IDR')
                    ->sortable(),

                TextInputColumn::make('price_list_price')
                    ->label('Price List Price')
                    ->type('number')
                    ->step(0.01)
                    ->rules(['numeric', 'min:0'])
                    ->beforeStateUpdated(function ($record, $state) {
                        $priceList = $this->getRecord();
                        
                        if ($state) {
                            $priceList->addProduct(
                                $record->id,
                                $state,
                                $record->price_list_cost_price ?: $record->cost_price
                            );
                        } else {
                            $priceList->removeProduct($record->id);
                        }
                    }),

                TextInputColumn::make('price_list_cost_price')
                    ->label('Cost Price')
                    ->type('number')
                    ->step(0.01)
                    ->rules(['numeric', 'min:0'])
                    ->beforeStateUpdated(function ($record, $state) {
                        if ($record->price_list_item_id) {
                            PriceListItem::where('id', $record->price_list_item_id)
                                ->update(['cost_price' => $state]);
                        }
                    }),

                BooleanColumn::make('in_price_list')
                    ->label('In Price List')
                    ->sortable(),
            ])
            ->filters([
                TernaryFilter::make('in_price_list')
                    ->label('In Price List')
                    ->placeholder('All products')
                    ->trueLabel('In price list only')
                    ->falseLabel('Not in price list only'),

                SelectFilter::make('category_id')
                    ->label('Category')
                    ->relationship('category', 'name'),
            ])
            ->actions([
                Tables\Actions\Action::make('toggle')
                    ->label(fn ($record) => $record->in_price_list ? 'Remove' : 'Add')
                    ->icon(fn ($record) => $record->in_price_list ? 'heroicon-o-minus-circle' : 'heroicon-o-plus-circle')
                    ->color(fn ($record) => $record->in_price_list ? 'danger' : 'success')
                    ->action(function ($record) {
                        $priceList = $this->getRecord();
                        
                        if ($record->in_price_list) {
                            $priceList->removeProduct($record->id);
                            Notification::make()
                                ->success()
                                ->title('Product Removed')
                                ->body("Removed {$record->name} from price list")
                                ->send();
                        } else {
                            $priceList->addProduct(
                                $record->id,
                                $record->price,
                                $record->cost_price
                            );
                            Notification::make()
                                ->success()
                                ->title('Product Added')
                                ->body("Added {$record->name} to price list")
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('add_to_price_list')
                    ->label('Add to Price List')
                    ->icon('heroicon-o-plus-circle')
                    ->color('success')
                    ->action(function ($records) {
                        $priceList = $this->getRecord();
                        $added = 0;
                        
                        foreach ($records as $record) {
                            if (!$record->in_price_list) {
                                $priceList->addProduct(
                                    $record->id,
                                    $record->price,
                                    $record->cost_price
                                );
                                $added++;
                            }
                        }

                        Notification::make()
                            ->success()
                            ->title('Products Added')
                            ->body("Added {$added} products to price list")
                            ->send();
                    }),

                Tables\Actions\BulkAction::make('remove_from_price_list')
                    ->label('Remove from Price List')
                    ->icon('heroicon-o-minus-circle')
                    ->color('danger')
                    ->action(function ($records) {
                        $priceList = $this->getRecord();
                        $removed = 0;
                        
                        foreach ($records as $record) {
                            if ($record->in_price_list) {
                                $priceList->removeProduct($record->id);
                                $removed++;
                            }
                        }

                        Notification::make()
                            ->success()
                            ->title('Products Removed')
                            ->body("Removed {$removed} products from price list")
                            ->send();
                    }),
            ])
            ->defaultSort('name');
    }
}
